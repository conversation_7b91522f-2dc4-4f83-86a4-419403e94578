<?php

namespace app\api\controller;

use app\BaseController;
use app\api\model\SheetKpi as SheetKpiModel;

class SheetKpi extends BaseController
{
    /**
     * 获取KPI列表
     * @return \think\response\Json
     */
    public function index()
    {
        try {
            // 获取筛选参数
            $filter = [
                'filter_name' => $this->request->param('filter_name', ''),
                'filter_months' => $this->request->param('filter_months', date('Y-m')),
                'sort' => $this->request->param('sort', 'score'),
                'order' => $this->request->param('order', 'ASC'),
                'page' => (int)$this->request->param('page', 1),
                'limit' => (int)$this->request->param('limit', 15),
            ];

            // 实例化KPI模型
            $kpiModel = new SheetKpiModel();

            // 获取KPI列表数据
            $result = $kpiModel->getSheetKpi($filter);

            // 处理KPI数据
            $kpiList = [];
            foreach ($result as $item) {
                $extra = strval($item['extra_info'] ?? 0);
                $score = strval($item['score'] ?? 0);

                // 计算扣款和应发岗位津贴
                $deductMoney = bcsub($extra, bcmul($score, bcmul($extra, '0.01', 10), 10), 2);
                $extraInfoScore = bcsub($extra, $deductMoney, 2);

                $kpiList[] = [
                    'id' => $item['id'],
                    'union_id' => $item['union_id'],
                    'real_name' => $item['real_name'],
                    'months' => $item['months'],
                    'score' => $item['score'],
                    'extra_info' => !is_null($item['extra_info']) ? number_format($item['extra_info'], 2) : '--',
                    'deduct_money' => !is_null($item['extra_info']) ? $deductMoney : '--',
                    'extra_info_score' => !is_null($item['extra_info']) ? $extraInfoScore : '--',
                ];
            }

            // 获取总数
            $total = $kpiModel->getSheetKpiTotal($filter);

            // 构建返回数据
            $data = [
                'kpi_list' => $kpiList,
                'pagination' => [
                    'total' => $total,
                    'per_page' => $filter['limit'],
                    'current_page' => $filter['page'],
                    'last_page' => $filter['limit'] > 0 ? ceil($total / $filter['limit']) : 1,
                ],
                'filters' => [
                    'filter_name' => $filter['filter_name'],
                    'filter_months' => $filter['filter_months'],
                ],
                'sort' => [
                    'field' => $filter['sort'],
                    'order' => $filter['order'],
                ],
                'months' => ['01','02','03','04','05','06','07','08','09','10','11','12'],
            ];

            return $this->success($data, '获取KPI列表成功');

        } catch (\Exception $e) {
            return $this->error('获取KPI列表失败：' . $e->getMessage());
        }
    }

    /**
     * 导出KPI数据
     * @return \think\response\Json
     */
    public function export()
    {
        try {
            // 获取筛选参数
            $filter = [
                'filter_name' => $this->request->param('filter_name', ''),
                'filter_months' => $this->request->param('filter_months', date('Y-m')),
                'sort' => $this->request->param('sort', 'score'),
                'order' => $this->request->param('order', 'ASC'),
            ];

            // 实例化KPI模型
            $kpiModel = new SheetKpiModel();

            // 获取所有KPI数据（不分页）
            $results = $kpiModel->getKpiAlgo($filter);

            // 构建导出数据
            $exportData = [];
            $exportData[] = ['姓名', '月份', '绩效分数', '岗位津贴', '扣款', '应发岗位津贴'];

            foreach ($results as $item) {
                $extra = strval($item['extra_info'] ?? 0);
                $score = strval($item['score'] ?? 0);

                $deductMoney = bcsub($extra, bcmul($score, bcmul($extra, '0.01', 10), 10), 2);
                $extraInfoScore = bcsub($extra, $deductMoney, 2);

                $exportData[] = [
                    $item['real_name'],
                    $filter['filter_months'] ?? '',
                    number_format($item['score'], 2) ?? '--',
                    $item['extra_info'],
                    !is_null($item['extra_info']) ? $deductMoney : '--',
                    !is_null($item['extra_info']) ? $extraInfoScore : '--',
                ];
            }

            // 返回导出数据
            $data = [
                'filename' => '待办事项' . $filter['filter_months'] . '月绩效' . date('Y-m-d'),
                'export_data' => $exportData,
            ];

            return $this->success($data, '导出数据准备成功');

        } catch (\Exception $e) {
            return $this->error('导出数据失败：' . $e->getMessage());
        }
    }

    /**
     * 获取KPI详情
     * @return \think\response\Json
     */
    public function detail()
    {
        try {
            // 获取筛选参数
            $union_id = $this->request->param('union_id', '');
            $filter_months = $this->request->param('ymonths', '');

            if (empty($union_id)) {
                return $this->error('用户ID不能为空');
            }

            // 实例化KPI模型
            $kpiModel = new SheetKpiModel();

            // 获取KPI详情数据
            $result = $kpiModel->getSheetKpiDetail($union_id,$filter_months);

            // 处理详情数据
            $detailList = [];
            foreach ($result as $item) {
                $detailList[] = [
                    'id' => $item['id'],
                    'union_id' => $item['union_id'],
                    'real_name' => $item['real_name'],
                    'create_time' => date('Y-m-d H:i:s', $item['create_time']),
                    'deadline' => date('Y-m-d', $item['deadline']),
                    'remark' => $item['remark'],
                    'is_compensation' => $item['is_compensation'] ?? 0,
                ];
            }

            // 获取总数
            $total = $kpiModel->getSheetKpiDetailTotal($union_id,$filter_months);

            // 构建返回数据
            $data = [
                'detail_list' => $detailList,
                'pagination' => [
                    'total' => $total,
                ],

            ];

            return $this->success($data, '获取KPI详情成功');

        } catch (\Exception $e) {
            return $this->error('获取KPI详情失败：' . $e->getMessage());
        }
    }

    /**
     * 补偿分数
     * @return \think\response\Json
     */
    public function compensationScore()
    {
        try {
            // 验证请求方法
            if (!$this->request->isPost()) {
                return $this->error('请求方法错误');
            }

            // 获取参数
            $id = (int)$this->request->param('id', 0);
            $ymonths = $this->request->param('ymonths', '');
            $unionId = (int)$this->request->param('union_id', 0);

            if (!$id) {
                return $this->error('无效的请求参数！');
            }

            if (empty($ymonths) || !$unionId) {
                return $this->error('缺少必要参数！');
            }


            // 实例化KPI模型
            $kpiModel = new SheetKpiModel();

            // 检查补偿状态
            $info = $kpiModel->findCompensationStatus($id);
            if (!$info) {
                return $this->error('记录不存在！');
            }

            if ($info['is_compensation'] == 1) {
                return $this->error('该扣分已失效，请刷新页面！');
            }

            // 更新补偿状态
            $updateStatusResult = $kpiModel->updateCompensationStatus($id);
            if (!$updateStatusResult) {
                return $this->error('更新补偿状态失败！');
            }

            // 加分
            $updateScoreResult = $kpiModel->updateCompensationScore($ymonths, $unionId);
            if (!$updateScoreResult) {
                return $this->error('更新分数失败！');
            }

            return $this->success([], '补分操作成功！');

        } catch (\Exception $e) {
            return $this->error('补分操作失败：' . $e->getMessage());
        }
    }
}
